import 'dart:io';
import 'dart:typed_data';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:image_gallery_saver/image_gallery_saver.dart';
import 'package:permission_handler/permission_handler.dart';

import '../utils/permission_helper.dart';

/// 图片保存服务
class ImageSaveService {
  static final Dio _dio = Dio();

  /// 保存网络图片到相册
  static Future<bool> saveNetworkImageToGallery(
    String imageUrl, {
    BuildContext? context,
    String? fileName,
  }) async {
    try {
      // 请求存储权限
      bool hasPermission = true; // 默认为true，以防context为null
      if (context != null) {
        hasPermission = await PermissionHelper.requestSaveImagePermission(context);
      }
      
      if (!hasPermission) {
        // PermissionHelper已经处理了用户反馈，这里可以直接返回
        return false;
      }

      // 下载图片
      final response = await _dio.get(
        imageUrl,
        options: Options(responseType: ResponseType.bytes),
      );

      if (response.statusCode == 200) {
        final Uint8List imageBytes = Uint8List.fromList(response.data);
        
        // 保存到相册
        final result = await ImageGallerySaver.saveImage(
          imageBytes,
          name: fileName ?? 'hera_wedding_photo_${DateTime.now().millisecondsSinceEpoch}',
          quality: 100,
        );

        if (result['isSuccess'] == true) {
          if (context != null) {
            _showSuccessMessage(context);
          }
          return true;
        } else {
          if (context != null) {
            _showErrorMessage(context, '保存失败：${result['errorMessage'] ?? '未知错误'}');
          }
          return false;
        }
      } else {
        if (context != null) {
          _showErrorMessage(context, '下载图片失败');
        }
        return false;
      }
    } catch (e) {
      if (context != null) {
        _showErrorMessage(context, '保存失败：$e');
      }
      return false;
    }
  }

  /// 保存本地图片到相册
  static Future<bool> saveLocalImageToGallery(
    File imageFile, {
    BuildContext? context,
    String? fileName,
  }) async {
    try {
      // 请求存储权限
      bool hasPermission = true;
      if (context != null) {
        hasPermission = await PermissionHelper.requestSaveImagePermission(context);
      }
      
      if (!hasPermission) {
        // PermissionHelper已经处理了用户反馈
        return false;
      }

      // 读取图片数据
      final Uint8List imageBytes = await imageFile.readAsBytes();
      
      // 保存到相册
      final result = await ImageGallerySaver.saveImage(
        imageBytes,
        name: fileName ?? 'hera_photo_${DateTime.now().millisecondsSinceEpoch}',
        quality: 100,
      );

      if (result['isSuccess'] == true) {
        if (context != null) {
          _showSuccessMessage(context);
        }
        return true;
      } else {
        if (context != null) {
          _showErrorMessage(context, '保存失败：${result['errorMessage'] ?? '未知错误'}');
        }
        return false;
      }
    } catch (e) {
      if (context != null) {
        _showErrorMessage(context, '保存失败：$e');
      }
      return false;
    }
  }

  /// 批量保存图片到相册
  static Future<Map<String, int>> saveBatchImages({
    List<String>? networkUrls,
    List<File>? localFiles,
    BuildContext? context,
    Function(int current, int total)? onProgress,
  }) async {
    int successCount = 0;
    int failureCount = 0;
    int totalCount = (networkUrls?.length ?? 0) + (localFiles?.length ?? 0);

    if (totalCount == 0) {
      return {'success': 0, 'failure': 0};
    }

    // 请求存储权限
    bool hasPermission = true;
    if (context != null) {
      hasPermission = await PermissionHelper.requestSaveImagePermission(context);
    }
    
    if (!hasPermission) {
      if (context != null) {
        _showErrorMessage(context, '无法保存图片：未获得相册权限');
      }
      return {'success': 0, 'failure': totalCount};
    }

    int currentIndex = 0;

    // 保存网络图片
    if (networkUrls != null) {
      for (final url in networkUrls) {
        currentIndex++;
        onProgress?.call(currentIndex, totalCount);
        
        final success = await saveNetworkImageToGallery(url);
        if (success) {
          successCount++;
        } else {
          failureCount++;
        }
      }
    }

    // 保存本地图片
    if (localFiles != null) {
      for (final file in localFiles) {
        currentIndex++;
        onProgress?.call(currentIndex, totalCount);
        
        final success = await saveLocalImageToGallery(file);
        if (success) {
          successCount++;
        } else {
          failureCount++;
        }
      }
    }

    // 显示批量保存结果
    if (context != null) {
      _showBatchSaveResult(context, successCount, failureCount);
    }

    return {'success': successCount, 'failure': failureCount};
  }

  /// 请求存储权限
  // - 此方法已废弃，所有权限请求均由PermissionHelper处理 -
  /*
  static Future<bool> _requestStoragePermission() async {
    try {
      if (Platform.isAndroid) {
        // Android 权限处理
        PermissionStatus photosStatus = await Permission.photos.status;
        
        // 如果未请求过权限，直接请求
        if (photosStatus.isDenied) {
          photosStatus = await Permission.photos.request();
        }
        
        // 如果权限被永久拒绝，尝试请求存储权限
        if (photosStatus.isPermanentlyDenied) {
          PermissionStatus storageStatus = await Permission.storage.status;
          if (storageStatus.isDenied) {
            storageStatus = await Permission.storage.request();
          }
          return storageStatus.isGranted;
        }
        
        return photosStatus.isGranted;
      } else if (Platform.isIOS) {
        // iOS 权限处理
        PermissionStatus photosStatus = await Permission.photos.status;
        
        if (photosStatus.isDenied) {
          photosStatus = await Permission.photos.request();
        }
        
        return photosStatus.isGranted;
      }
    } catch (e) {
      print('权限请求错误: $e');
      return false;
    }

    return true;
  }
  */

  /// 显示成功消息
  static void _showSuccessMessage(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(Icons.check_circle, color: Colors.white),
            const SizedBox(width: 8),
            const Text('图片已保存到相册'),
          ],
        ),
        backgroundColor: Colors.green,
        duration: const Duration(seconds: 2),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }

  /// 显示错误消息
  static void _showErrorMessage(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(Icons.error, color: Colors.white),
            const SizedBox(width: 8),
            Expanded(child: Text(message)),
          ],
        ),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 3),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }

  /// 显示批量保存结果
  static void _showBatchSaveResult(BuildContext context, int successCount, int failureCount) {
    final totalCount = successCount + failureCount;
    final isAllSuccess = failureCount == 0;
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(
              isAllSuccess ? Icons.check_circle : Icons.warning,
              color: Colors.white,
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                isAllSuccess
                    ? '所有图片保存成功！($totalCount张)'
                    : '保存完成：成功$successCount张，失败$failureCount张',
              ),
            ),
          ],
        ),
        backgroundColor: isAllSuccess ? Colors.green : Colors.orange,
        duration: const Duration(seconds: 3),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }
} 