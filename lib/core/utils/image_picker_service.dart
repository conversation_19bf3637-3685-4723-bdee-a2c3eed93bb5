import 'dart:io';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';

/// 图片选择服务
class ImagePickerService {
  static final ImagePicker _picker = ImagePicker();

  /// 从相机拍照
  static Future<File?> pickImageFromCamera(BuildContext context) async {
    try {
      final XFile? image = await _picker.pickImage(
        source: ImageSource.camera,
        maxWidth: 1920,
        maxHeight: 1080,
        imageQuality: 85,
      );

      if (image != null) {
        return File(image.path);
      }
      return null;
    } catch (e) {
      _handleError(context, e, '相机');
      return null;
    }
  }

  /// 从相册选择图片
  static Future<File?> pickImageFromGallery(BuildContext context) async {
    try {
      final XFile? image = await _picker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 1920,
        maxHeight: 1080,
        imageQuality: 85,
      );

      if (image != null) {
        return File(image.path);
      }
      return null;
    } catch (e) {
      _handleError(context, e, '照片库');
      return null;
    }
  }

  /// 选择多张图片
  static Future<List<File>> pickMultipleImages(BuildContext context, {int limit = 5}) async {
    try {
      final List<XFile> images = await _picker.pickMultiImage(
        maxWidth: 1920,
        maxHeight: 1080,
        imageQuality: 85,
        limit: limit,
      );

      return images.map((image) => File(image.path)).toList();
    } catch (e) {
      _handleError(context, e, '照片库');
      return [];
    }
  }

  /// 显示图片选择底部菜单
  static Future<File?> showImagePickerBottomSheet(BuildContext context) async {
    return await showModalBottomSheet<File?>(
      context: context,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            const SizedBox(height: 20),
            Text(
              '选择图片',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 20),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                _buildPickerOption(
                  context: context,
                  icon: Icons.camera_alt,
                  label: '拍照',
                  onTap: () async {
                    final file = await pickImageFromCamera(context);
                    if (context.mounted) Navigator.pop(context, file);
                  },
                ),
                _buildPickerOption(
                  context: context,
                  icon: Icons.photo_library,
                  label: '相册',
                  onTap: () async {
                    final file = await pickImageFromGallery(context);
                    if (context.mounted) Navigator.pop(context, file);
                  },
                ),
              ],
            ),
            const SizedBox(height: 20),
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('取消'),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建选择器选项
  static Widget _buildPickerOption({
    required BuildContext context,
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: 100,
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Theme.of(context).primaryColor.withOpacity(0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: Theme.of(context).primaryColor.withOpacity(0.3),
          ),
        ),
        child: Column(
          children: [
            Icon(
              icon,
              size: 32,
              color: Theme.of(context).primaryColor,
            ),
            const SizedBox(height: 8),
            Text(
              label,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Theme.of(context).primaryColor,
              ),
            ),
          ],
        ),
      ),
    );
  }



  /// 处理错误
  static void _handleError(BuildContext context, dynamic error, String source) {
    String message = '选择图片失败';
    
    if (error.toString().contains('photo_access_denied')) {
      message = '相册访问被拒绝，请在设置中开启权限';
    } else if (error.toString().contains('camera_access_denied')) {
      message = '相机访问被拒绝，请在设置中开启权限';
    }

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
      ),
    );
  }
} 