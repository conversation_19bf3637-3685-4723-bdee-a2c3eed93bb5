import 'dart:io';
import 'dart:typed_data';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:image_gallery_saver/image_gallery_saver.dart';
import 'package:permission_handler/permission_handler.dart';

import '../utils/permission_helper.dart';
import 'permission_service.dart';
import '../utils/log_service.dart';

/// 图片保存服务
class ImageSaveService {
  static final Dio _dio = Dio();
  static final PermissionService _permissionService = PermissionService();
  static final LogService _logger = LogService();

  /// 保存网络图片到相册
  static Future<bool> saveNetworkImageToGallery(
    String imageUrl, {
    BuildContext? context,
    String? fileName,
  }) async {
    try {
      _logger.i('开始保存网络图片到相册: $imageUrl');

      // 请求存储权限
      bool hasPermission = await _requestSavePermission(context);

      if (!hasPermission) {
        _logger.w('保存图片失败：权限被拒绝');
        return false;
      }

      // 下载图片
      final response = await _dio.get(
        imageUrl,
        options: Options(responseType: ResponseType.bytes),
      );

      if (response.statusCode == 200) {
        final Uint8List imageBytes = Uint8List.fromList(response.data);
        
        // 保存到相册
        final result = await ImageGallerySaver.saveImage(
          imageBytes,
          name: fileName ?? 'hera_wedding_photo_${DateTime.now().millisecondsSinceEpoch}',
          quality: 100,
        );

        if (result['isSuccess'] == true) {
          if (context != null && context.mounted) {
            _showSuccessMessage(context);
          }
          return true;
        } else {
          if (context != null && context.mounted) {
            _showErrorMessage(context, '保存失败：${result['errorMessage'] ?? '未知错误'}');
          }
          return false;
        }
      } else {
        if (context != null && context.mounted) {
          _showErrorMessage(context, '下载图片失败');
        }
        return false;
      }
    } catch (e) {
      if (context != null && context.mounted) {
        _showErrorMessage(context, '保存失败：$e');
      }
      return false;
    }
  }

  /// 保存本地图片到相册
  static Future<bool> saveLocalImageToGallery(
    File imageFile, {
    BuildContext? context,
    String? fileName,
  }) async {
    try {
      // 请求存储权限
      bool hasPermission = true;
      if (context != null) {
        hasPermission = await PermissionHelper.requestSaveImagePermission(context);
      }
      
      if (!hasPermission) {
        // PermissionHelper已经处理了用户反馈
        return false;
      }

      // 读取图片数据
      final Uint8List imageBytes = await imageFile.readAsBytes();
      
      // 保存到相册
      final result = await ImageGallerySaver.saveImage(
        imageBytes,
        name: fileName ?? 'hera_photo_${DateTime.now().millisecondsSinceEpoch}',
        quality: 100,
      );

      if (result['isSuccess'] == true) {
        if (context != null && context.mounted) {
          _showSuccessMessage(context);
        }
        return true;
      } else {
        if (context != null && context.mounted) {
          _showErrorMessage(context, '保存失败：${result['errorMessage'] ?? '未知错误'}');
        }
        return false;
      }
    } catch (e) {
      if (context != null && context.mounted) {
        _showErrorMessage(context, '保存失败：$e');
      }
      return false;
    }
  }

  /// 批量保存图片到相册
  static Future<Map<String, int>> saveBatchImages({
    List<String>? networkUrls,
    List<File>? localFiles,
    BuildContext? context,
    Function(int current, int total)? onProgress,
  }) async {
    int successCount = 0;
    int failureCount = 0;
    int totalCount = (networkUrls?.length ?? 0) + (localFiles?.length ?? 0);

    if (totalCount == 0) {
      return {'success': 0, 'failure': 0};
    }

    // 请求存储权限
    bool hasPermission = true;
    if (context != null) {
      hasPermission = await PermissionHelper.requestSaveImagePermission(context);
    }
    
    if (!hasPermission) {
      if (context != null && context.mounted) {
        _showErrorMessage(context, '无法保存图片：未获得相册权限');
      }
      return {'success': 0, 'failure': totalCount};
    }

    int currentIndex = 0;

    // 保存网络图片
    if (networkUrls != null) {
      for (final url in networkUrls) {
        currentIndex++;
        onProgress?.call(currentIndex, totalCount);
        
        final success = await saveNetworkImageToGallery(url);
        if (success) {
          successCount++;
        } else {
          failureCount++;
        }
      }
    }

    // 保存本地图片
    if (localFiles != null) {
      for (final file in localFiles) {
        currentIndex++;
        onProgress?.call(currentIndex, totalCount);
        
        final success = await saveLocalImageToGallery(file);
        if (success) {
          successCount++;
        } else {
          failureCount++;
        }
      }
    }

    // 显示批量保存结果
    if (context != null && context.mounted) {
      _showBatchSaveResult(context, successCount, failureCount);
    }

    return {'success': successCount, 'failure': failureCount};
  }

  /// 请求保存图片权限
  static Future<bool> _requestSavePermission(BuildContext? context) async {
    try {
      _logger.d('请求保存图片权限');

      // 检查当前权限状态
      final currentStatus = await _permissionService.checkPhotosPermission();
      _logger.d('当前相册权限状态: $currentStatus');

      if (currentStatus == PermissionResult.granted ||
          currentStatus == PermissionResult.limited) {
        return true;
      }

      // 如果权限被永久拒绝，显示设置对话框
      if (currentStatus == PermissionResult.permanentlyDenied) {
        if (context != null && context.mounted) {
          await _showPermissionDeniedDialog(context);
        }
        return false;
      }

      // 请求权限
      final result = await _permissionService.requestPhotosPermission();
      _logger.i('权限请求结果: $result');

      if (result == PermissionResult.granted ||
          result == PermissionResult.limited) {
        return true;
      }

      // 权限被拒绝，显示说明对话框
      if (context != null && context.mounted) {
        if (result == PermissionResult.permanentlyDenied) {
          await _showPermissionDeniedDialog(context);
        } else {
          _showPermissionRequiredMessage(context);
        }
      }

      return false;
    } catch (e) {
      _logger.e('请求保存权限失败', e);
      if (context != null && context.mounted) {
        _showErrorMessage(context, '权限请求失败：$e');
      }
      return false;
    }
  }

  /// 显示成功消息
  static void _showSuccessMessage(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(Icons.check_circle, color: Colors.white),
            const SizedBox(width: 8),
            const Text('图片已保存到相册'),
          ],
        ),
        backgroundColor: Colors.green,
        duration: const Duration(seconds: 2),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }

  /// 显示错误消息
  static void _showErrorMessage(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(Icons.error, color: Colors.white),
            const SizedBox(width: 8),
            Expanded(child: Text(message)),
          ],
        ),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 3),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }

  /// 显示批量保存结果
  static void _showBatchSaveResult(BuildContext context, int successCount, int failureCount) {
    final totalCount = successCount + failureCount;
    final isAllSuccess = failureCount == 0;

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(
              isAllSuccess ? Icons.check_circle : Icons.warning,
              color: Colors.white,
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                isAllSuccess
                    ? '所有图片保存成功！($totalCount张)'
                    : '保存完成：成功$successCount张，失败$failureCount张',
              ),
            ),
          ],
        ),
        backgroundColor: isAllSuccess ? Colors.green : Colors.orange,
        duration: const Duration(seconds: 3),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }

  /// 显示权限被拒绝对话框
  static Future<void> _showPermissionDeniedDialog(BuildContext context) async {
    return showDialog<void>(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.orange.shade50,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                Icons.photo_library_outlined,
                color: Colors.orange.shade600,
                size: 24,
              ),
            ),
            const SizedBox(width: 12),
            const Expanded(
              child: Text(
                '需要相册权限',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '为了保存生成的婚纱照到您的相册，需要开启相册访问权限。',
              style: TextStyle(fontSize: 14, height: 1.5),
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.blue.shade50,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '如何开启权限：',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Colors.blue.shade700,
                      fontSize: 13,
                    ),
                  ),
                  const SizedBox(height: 8),
                  const Text('1. 点击"去设置"按钮', style: TextStyle(fontSize: 12)),
                  const Text('2. 找到"权限"或"隐私"选项', style: TextStyle(fontSize: 12)),
                  const Text('3. 开启"相册"或"照片"权限', style: TextStyle(fontSize: 12)),
                  const Text('4. 返回应用重新保存', style: TextStyle(fontSize: 12)),
                ],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('稍后再说'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              openAppSettings();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
            ),
            child: const Text('去设置'),
          ),
        ],
      ),
    );
  }

  /// 显示权限必需提示
  static void _showPermissionRequiredMessage(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(Icons.info_outline, color: Colors.white),
            const SizedBox(width: 8),
            const Expanded(
              child: Text('需要相册权限才能保存图片，请在设置中开启'),
            ),
          ],
        ),
        backgroundColor: Colors.orange,
        duration: const Duration(seconds: 4),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        action: SnackBarAction(
          label: '去设置',
          textColor: Colors.white,
          onPressed: () => openAppSettings(),
        ),
      ),
    );
  }
}