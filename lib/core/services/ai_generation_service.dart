import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';

/// AI图片生成服务
class AIGenerationService {
  static const String _baseUrl = 'https://api.openai.com/v1';
  static const String _apiKey = 'YOUR_OPENAI_API_KEY'; // 请替换为实际的API密钥
  
  final Dio _dio;

  AIGenerationService() : _dio = Dio() {
    _dio.options.headers = {
      'Authorization': 'Bearer $_apiKey',
      'Content-Type': 'application/json',
    };
    _dio.options.connectTimeout = const Duration(seconds: 30);
    _dio.options.receiveTimeout = const Duration(seconds: 60);
  }

  /// 分析图片并生成婚纱照描述
  Future<String> analyzeImageForWeddingPhoto(File imageFile) async {
    try {
      // 将图片转换为base64
      final bytes = await imageFile.readAsBytes();
      final base64Image = base64Encode(bytes);
      
      final prompt = _buildWeddingPhotoPrompt();
      
      final requestData = {
        'model': 'gpt-4o',
        'messages': [
          {
            'role': 'user',
            'content': [
              {
                'type': 'text',
                'text': prompt,
              },
              {
                'type': 'image_url',
                'image_url': {
                  'url': 'data:image/jpeg;base64,$base64Image',
                  'detail': 'high'
                }
              }
            ]
          }
        ],
        'max_tokens': 500,
        'temperature': 0.7,
      };

      final response = await _dio.post(
        '$_baseUrl/chat/completions',
        data: requestData,
      );

      if (response.statusCode == 200) {
        final data = response.data;
        final content = data['choices'][0]['message']['content'];
        return content as String;
      } else {
        throw Exception('API调用失败: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('AI分析错误: $e');
      throw Exception('图片分析失败: $e');
    }
  }

  /// 分析多张图片并生成综合婚纱照描述
  Future<String> analyzeMultipleImagesForWeddingPhoto(List<File> imageFiles) async {
    try {
      List<Map<String, dynamic>> imageContents = [];
      
      // 处理所有图片
      for (int i = 0; i < imageFiles.length; i++) {
        final bytes = await imageFiles[i].readAsBytes();
        final base64Image = base64Encode(bytes);
        
        imageContents.add({
          'type': 'image_url',
          'image_url': {
            'url': 'data:image/jpeg;base64,$base64Image',
            'detail': 'high'
          }
        });
      }

      final prompt = _buildMultipleImagesWeddingPrompt(imageFiles.length);
      
      final requestData = {
        'model': 'gpt-4o',
        'messages': [
          {
            'role': 'user',
            'content': [
              {
                'type': 'text',
                'text': prompt,
              },
              ...imageContents,
            ]
          }
        ],
        'max_tokens': 800,
        'temperature': 0.7,
      };

      final response = await _dio.post(
        '$_baseUrl/chat/completions',
        data: requestData,
      );

      if (response.statusCode == 200) {
        final data = response.data;
        final content = data['choices'][0]['message']['content'];
        return content as String;
      } else {
        throw Exception('API调用失败: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('AI分析错误: $e');
      throw Exception('图片分析失败: $e');
    }
  }

  /// 使用DALL-E生成婚纱照
  Future<String> generateWeddingPhotoWithDALLE(String description) async {
    try {
      final enhancedPrompt = _enhancePromptForDALLE(description);
      
      final requestData = {
        'model': 'dall-e-3',
        'prompt': enhancedPrompt,
        'n': 1,
        'size': '1024x1024',
        'quality': 'hd',
        'style': 'vivid',
        'response_format': 'url',
      };

      final response = await _dio.post(
        '$_baseUrl/images/generations',
        data: requestData,
      );

      if (response.statusCode == 200) {
        final data = response.data;
        final imageUrl = data['data'][0]['url'];
        return imageUrl as String;
      } else {
        throw Exception('DALL-E生成失败: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('DALL-E生成错误: $e');
      throw Exception('图片生成失败: $e');
    }
  }

  /// 构建单张图片婚纱照分析提示词
  String _buildWeddingPhotoPrompt() {
    return '''
请仔细分析这张照片中的人物特征，并为AI生成婚纱照提供详细描述。请关注以下要点：

1. **人物特征**：
   - 面部特征（脸型、眼睛、鼻子、嘴唇等）
   - 肤色和肤质
   - 发型和发色
   - 身材比例

2. **婚纱照风格建议**：
   - 适合的婚纱款式（如A字裙、鱼尾裙、公主裙等）
   - 拍摄场景建议（如教堂、花园、海边、室内等）
   - 灯光和氛围建议
   - pose和表情建议

3. **色彩搭配**：
   - 主色调建议
   - 配饰颜色搭配
   - 背景色彩建议

请用中文回答，并提供一个详细的、适合用于AI图片生成的描述，重点突出婚纱照的浪漫、优雅和梦幻感。
''';
  }

  /// 构建多张图片婚纱照分析提示词
  String _buildMultipleImagesWeddingPrompt(int imageCount) {
    return '''
请分析这${imageCount}张照片中的人物特征，并为AI生成婚纱照提供综合性的详细描述。

**分析要求**：
1. **综合人物特征**：
   - 从多个角度分析面部特征的一致性
   - 确定最佳的面部角度和表情
   - 分析身材比例和姿态
   - 发型发色的最佳展现方式

2. **婚纱照创意方案**：
   - 基于人物气质选择最适合的婚纱风格
   - 推荐3-5种不同的拍摄场景和主题
   - 灯光布置和拍摄角度建议
   - 整体构图和氛围营造

3. **风格定位**：
   - 经典优雅风格
   - 现代时尚风格  
   - 浪漫梦幻风格
   - 自然清新风格

4. **技术细节**：
   - 色彩搭配方案
   - 妆容建议
   - 配饰选择
   - 后期处理建议

请用中文提供一个完整的、专业的婚纱照AI生成描述，确保生成的婚纱照能够展现出最美的效果。
''';
  }

  /// 增强DALL-E生成提示词
  String _enhancePromptForDALLE(String originalDescription) {
    final basePrompt = '''
High-quality professional wedding photography, beautiful bride in elegant wedding dress, 
studio lighting, romantic atmosphere, dreamy background, soft focus, 
professional photographer style, wedding magazine quality, 8K resolution, photorealistic.

Based on the analysis: $originalDescription

Key requirements:
- Professional wedding photography style
- Elegant and romantic atmosphere  
- High-quality lighting and composition
- Beautiful wedding dress details
- Dreamy and enchanting mood
- Magazine-quality aesthetic
''';
    
    return basePrompt;
  }

  /// 生成婚纱照的完整流程
  Future<GenerationResult> generateWeddingPhotos(List<File> images) async {
    try {
      // 步骤1: 分析图片
      String analysis;
      if (images.length == 1) {
        analysis = await analyzeImageForWeddingPhoto(images.first);
      } else {
        analysis = await analyzeMultipleImagesForWeddingPhoto(images);
      }

      // 步骤2: 生成图片
      final generatedImageUrl = await generateWeddingPhotoWithDALLE(analysis);

      return GenerationResult(
        success: true,
        analysis: analysis,
        generatedImageUrl: generatedImageUrl,
        message: '婚纱照生成成功！',
      );
    } catch (e) {
      return GenerationResult(
        success: false,
        analysis: '',
        generatedImageUrl: '',
        message: '生成失败: $e',
      );
    }
  }
}

/// 生成结果数据类
class GenerationResult {
  final bool success;
  final String analysis;
  final String generatedImageUrl;
  final String message;

  GenerationResult({
    required this.success,
    required this.analysis,
    required this.generatedImageUrl,
    required this.message,
  });
} 