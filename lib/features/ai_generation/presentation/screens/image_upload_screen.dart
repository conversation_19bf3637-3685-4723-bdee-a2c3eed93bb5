import 'dart:io';
import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:image_picker/image_picker.dart';
import '../../../../core/services/permission_service.dart';
import '../../../../core/utils/log_service.dart';
import '../../../../shared/widgets/custom_button.dart';
import '../../../../shared/widgets/loading_overlay.dart';

import '../providers/ai_generation_providers.dart';
import '../widgets/image_upload_card.dart';

/// 图片上传页面
/// 
/// 功能特性:
/// 1. 自动权限管理 - 应用启动时自动请求所需权限，支持iOS 14+有限权限模式
/// 2. 直接相册访问 - 点击添加图片直接打开相册，无需用户确认
/// 3. 图片验证 - 自动验证格式、大小和完整性
/// 4. 友好的用户反馈 - 不同类型的提示信息
/// 5. 错误处理 - 详细的错误分类和处理
@RoutePage()
class ImageUploadScreen extends ConsumerStatefulWidget {
  const ImageUploadScreen({super.key});

  @override
  ConsumerState<ImageUploadScreen> createState() => _ImageUploadScreenState();
}

class _ImageUploadScreenState extends ConsumerState<ImageUploadScreen> {
  final LogService _logger = LogService();
  final ImagePicker _picker = ImagePicker();
  final PermissionService _permissionService = PermissionService();
  List<String> _selectedImages = [];
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _logger.i('图片上传页面初始化');
    _checkInitialPermissions();
  }

  /// 检查初始权限状态
  Future<void> _checkInitialPermissions() async {
    _logger.d('检查初始权限状态');

    // 直接请求相册权限
    await _requestPhotosPermissionQuietly();
    
    // 直接请求相机权限
    await _requestCameraPermissionQuietly();
    
    // 直接请求通知权限
    await _requestNotificationPermissionQuietly();
  }

  /// 静默请求相册权限
  Future<void> _requestPhotosPermissionQuietly() async {
    try {
      final status = await _permissionService.checkPhotosPermission();
      if (status == PermissionResult.denied) {
        _logger.d('静默请求相册权限');
        await _permissionService.requestPhotosPermission();
      }
    } catch (e) {
      _logger.e('静默请求相册权限失败: $e');
    }
  }

  /// 静默请求相机权限
  Future<void> _requestCameraPermissionQuietly() async {
    try {
      final status = await _permissionService.checkCameraPermission();
      if (status == PermissionResult.denied) {
        _logger.d('静默请求相机权限');
        await _permissionService.requestCameraPermission();
      }
    } catch (e) {
      _logger.e('静默请求相机权限失败: $e');
    }
  }

  /// 静默请求通知权限
  Future<void> _requestNotificationPermissionQuietly() async {
    try {
      final status = await _permissionService.checkNotificationPermission();
      if (status == PermissionResult.denied) {
        _logger.d('静默请求通知权限');
        await _permissionService.requestNotificationPermission();
      }
    } catch (e) {
      _logger.e('静默请求通知权限失败: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    // final aiGenerationState = ref.watch(aiGenerationProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('上传照片'),
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
        elevation: 0,
      ),
      body: LoadingOverlay(
        isLoading: _isLoading,
        child: Padding(
          padding: EdgeInsets.all(16.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildInstructions(),
              SizedBox(height: 24.h),
              _buildImageGrid(),
              SizedBox(height: 24.h),
              _buildUploadButtons(),
              const Spacer(),
              _buildContinueButton(),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建说明文本
  Widget _buildInstructions() {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.blue.shade50,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: Colors.blue.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.info_outline, color: Colors.blue.shade600, size: 20.sp),
              SizedBox(width: 8.w),
              Text(
                '上传要求',
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.bold,
                  color: Colors.blue.shade600,
                ),
              ),
            ],
          ),
          SizedBox(height: 8.h),
          Text(
            '• 支持JPG、PNG、HEIC格式\n'
            '• 单张图片不超过10MB\n'
            '• 建议上传1-3张清晰的正面照\n'
            '• 确保人脸清晰可见，光线充足',
            style: TextStyle(
              fontSize: 14.sp,
              color: Colors.grey.shade700,
              height: 1.5,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建图片网格
  Widget _buildImageGrid() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '已选择照片 (${_selectedImages.length}/3)',
          style: TextStyle(
            fontSize: 16.sp,
            fontWeight: FontWeight.bold,
          ),
        ),
        SizedBox(height: 12.h),
        SizedBox(
          height: 120.h,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: _selectedImages.length + (_selectedImages.length < 3 ? 1 : 0),
            itemBuilder: (context, index) {
              if (index < _selectedImages.length) {
                return ImageUploadCard(
                  imagePath: _selectedImages[index],
                  onRemove: () => _removeImage(index),
                );
              } else {
                return _buildAddImageCard();
              }
            },
          ),
        ),
      ],
    );
  }

  /// 构建添加图片卡片
  Widget _buildAddImageCard() {
    return Container(
      width: 120.w,
      margin: EdgeInsets.only(right: 12.w),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300, width: 2),
        borderRadius: BorderRadius.circular(12.r),
        color: Colors.grey.shade50,
      ),
      child: InkWell(
        onTap: () => _pickImages(ImageSource.gallery),
        borderRadius: BorderRadius.circular(12.r),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.add_photo_alternate_outlined,
              size: 32.sp,
              color: Colors.grey.shade600,
            ),
            SizedBox(height: 8.h),
            Text(
              '添加照片',
              style: TextStyle(
                fontSize: 12.sp,
                color: Colors.grey.shade600,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建上传按钮
  Widget _buildUploadButtons() {
    return Row(
      children: [
        Expanded(
          child: CustomButton(
            text: '从相册选择',
            onPressed: () => _pickImages(ImageSource.gallery),
            backgroundColor: Colors.blue.shade50,
            textColor: Colors.blue.shade700,
            icon: Icons.photo_library_outlined,
          ),
        ),
        SizedBox(width: 12.w),
        Expanded(
          child: CustomButton(
            text: '拍照',
            onPressed: () => _pickImages(ImageSource.camera),
            backgroundColor: Colors.green.shade50,
            textColor: Colors.green.shade700,
            icon: Icons.camera_alt_outlined,
          ),
        ),
      ],
    );
  }

  /// 构建继续按钮
  Widget _buildContinueButton() {
    return CustomButton(
      text: '继续选择风格',
      onPressed: _selectedImages.isNotEmpty ? _continueToStyleSelection : null,
      isFullWidth: true,
    );
  }

  /// 选择图片
  Future<void> _pickImages(ImageSource source) async {
    _logger.d('开始选择图片: ${source.name}');

    try {
      // 检查权限
      if (!await _checkPermissions(source)) {
        _logger.w('权限检查失败');
        return;
      }

      setState(() => _isLoading = true);

      if (source == ImageSource.gallery) {
        await _pickFromGallery();
      } else {
        await _pickFromCamera();
      }
    } catch (e, stackTrace) {
      _logger.e('选择图片失败', e, stackTrace);
      _handleImagePickerError(e);
    } finally {
      setState(() => _isLoading = false);
    }
  }

  /// 从相册选择图片
  Future<void> _pickFromGallery() async {
    _logger.d('从相册选择多张图片');
    
    final images = await _picker.pickMultiImage(
      maxWidth: 1920,
      maxHeight: 1920,
      imageQuality: 85,
      requestFullMetadata: false, // 提高性能
    );

    if (images.isNotEmpty) {
      _logger.i('从相册选择了${images.length}张图片');
      final imagePaths = images.map((image) => image.path).toList();
      
      // 验证图片文件
      final validPaths = await _validateImageFiles(imagePaths);
      
      if (validPaths.isNotEmpty) {
        _addImages(validPaths);
      } else {
        _showErrorSnackBar('所选图片格式不支持或文件损坏');
      }
    } else {
      _logger.d('用户取消了图片选择');
    }
  }

  /// 从相机拍照
  Future<void> _pickFromCamera() async {
    _logger.d('使用相机拍照');
    
    final image = await _picker.pickImage(
      source: ImageSource.camera,
      maxWidth: 1920,
      maxHeight: 1920,
      imageQuality: 85,
      requestFullMetadata: false,
    );

    if (image != null) {
      _logger.i('拍摄了一张照片: ${image.path}');
      
      // 验证图片文件
      if (await _isValidImageFile(image.path)) {
        _addImages([image.path]);
      } else {
        _showErrorSnackBar('拍摄的照片格式不支持或文件损坏');
      }
    } else {
      _logger.d('用户取消了拍照');
    }
  }

  /// 验证图片文件列表
  Future<List<String>> _validateImageFiles(List<String> imagePaths) async {
    final validPaths = <String>[];
    
    for (final path in imagePaths) {
      if (await _isValidImageFile(path)) {
        validPaths.add(path);
      }
    }
    
    return validPaths;
  }

  /// 验证单个图片文件
  Future<bool> _isValidImageFile(String path) async {
    try {
      final file = File(path);
      
      // 检查文件是否存在
      if (!await file.exists()) {
        _logger.w('图片文件不存在: $path');
        return false;
      }
      
      // 检查文件大小（限制10MB）
      final fileSize = await file.length();
      if (fileSize > 10 * 1024 * 1024) {
        _logger.w('图片文件过大: ${fileSize}bytes');
        _showErrorSnackBar('图片文件过大，请选择小于10MB的图片');
        return false;
      }
      
      // 检查文件格式（通过扩展名）
      final extension = path.toLowerCase().split('.').last;
      if (!['jpg', 'jpeg', 'png', 'heic', 'heif'].contains(extension)) {
        _logger.w('不支持的图片格式: $extension');
        return false;
      }
      
      return true;
    } catch (e) {
      _logger.e('验证图片文件失败: $path', e);
      return false;
    }
  }

  /// 处理图片选择器错误
  void _handleImagePickerError(dynamic error) {
    String errorMessage = '选择图片失败';
    
    final errorString = error.toString().toLowerCase();
    
    if (errorString.contains('camera_access_denied') || 
        errorString.contains('camera access denied')) {
      errorMessage = '相机权限被拒绝，请在设置中开启相机权限';
    } else if (errorString.contains('photo_access_denied') || 
               errorString.contains('photo library access denied')) {
      errorMessage = '相册权限被拒绝，请在设置中开启相册权限';
    } else if (errorString.contains('invalid_image') || 
               errorString.contains('file_not_found')) {
      errorMessage = '图片文件无效或不存在';
    } else if (errorString.contains('permission_denied')) {
      errorMessage = '权限被拒绝，请检查应用权限设置';
    } else if (errorString.contains('network')) {
      errorMessage = '网络连接失败，请检查网络设置';
    }
    
    _showErrorSnackBar(errorMessage);
  }

  /// 检查权限
  Future<bool> _checkPermissions(ImageSource source) async {
    try {
      if (source == ImageSource.camera) {
        return await _checkCameraPermission();
      } else {
        return await _checkPhotosPermission();
      }
    } catch (e) {
      _logger.e('权限检查失败: $e');
      _showErrorSnackBar('权限检查失败，请在设置中手动开启权限');
      return false;
    }
  }

  /// 检查相机权限
  Future<bool> _checkCameraPermission() async {
    try {
      final status = await _permissionService.checkCameraPermission();

      switch (status) {
        case PermissionResult.granted:
          _logger.d('相机权限已获得');
          return true;

        case PermissionResult.denied:
          _logger.d('相机权限被拒绝，直接请求权限');
          final requestResult = await _permissionService.requestCameraPermission();
          
          if (requestResult == PermissionResult.granted) {
            _logger.i('相机权限请求成功');
            return true;
          } else if (requestResult == PermissionResult.permanentlyDenied) {
            _logger.w('相机权限被永久拒绝');
            await _showPermanentlyDeniedDialog(PermissionType.camera);
            return false;
          } else {
            _logger.w('相机权限请求失败: ${requestResult.name}');
            _showErrorSnackBar('相机权限被拒绝，无法使用拍照功能');
            return false;
          }

        case PermissionResult.permanentlyDenied:
          _logger.w('相机权限被永久拒绝');
          await _showPermanentlyDeniedDialog(PermissionType.camera);
          return false;

        case PermissionResult.restricted:
          _logger.w('相机权限受限');
          _showErrorSnackBar('设备限制无法使用相机功能');
          return false;

        default:
          _logger.w('相机权限状态未知: ${status.name}');
          _showErrorSnackBar('相机权限状态异常，请检查设备设置');
          return false;
      }
    } catch (e) {
      _logger.e('检查相机权限失败: $e');
      _showErrorSnackBar('相机权限检查失败，请稍后重试');
      return false;
    }
  }

  /// 检查相册权限
  Future<bool> _checkPhotosPermission() async {
    try {
      // 先检查当前权限状态
      final currentStatus = await _permissionService.checkPhotosPermission();
      
      // 如果已经有权限（包括有限权限），直接返回
      if (currentStatus == PermissionResult.granted || 
          currentStatus == PermissionResult.limited) {
        _logger.d('相册权限已获得: ${currentStatus.name}');
        if (currentStatus == PermissionResult.limited) {
          _showLimitedPermissionTip();
        }
        return true;
      }
      
      // 如果权限被永久拒绝，直接显示设置引导
      if (currentStatus == PermissionResult.permanentlyDenied) {
        _logger.w('相册权限被永久拒绝');
        await _showPermanentlyDeniedDialog(PermissionType.photos);
        return false;
      }
      
      // 直接请求权限，无需询问用户
      _logger.d('相册权限被拒绝，直接请求权限');
      final result = await _permissionService.requestPhotosPermission();
      
      switch (result) {
        case PermissionResult.granted:
          _logger.i('相册权限申请成功: 完全访问');
          _showSuccessSnackBar('相册权限获取成功');
          return true;
          
        case PermissionResult.limited:
          _logger.i('相册权限申请成功: 有限访问');
          // iOS 14+ 有限权限模式，仍然可以使用
          _showLimitedPermissionTip();
          return true;
          
        case PermissionResult.denied:
          _logger.w('相册权限申请被拒绝');
          _showErrorSnackBar('相册权限被拒绝，无法选择照片');
          return false;
          
        case PermissionResult.permanentlyDenied:
          _logger.w('相册权限被永久拒绝');
          await _showPermanentlyDeniedDialog(PermissionType.photos);
          return false;
          
        case PermissionResult.restricted:
          _logger.w('相册权限受限');
          _showErrorSnackBar('设备限制无法访问相册');
          return false;
      }
    } catch (e) {
      _logger.e('检查相册权限失败: $e');
      _showErrorSnackBar('权限检查失败，请稍后重试');
      return false;
    }
  }

  /// 显示有限权限提示
  void _showLimitedPermissionTip() {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Text('您已授权部分照片访问权限，如需访问更多照片可在设置中调整'),
        backgroundColor: Colors.orange,
        action: SnackBarAction(
          label: '去设置',
          textColor: Colors.white,
          onPressed: () => _permissionService.openSettings(),
        ),
        duration: const Duration(seconds: 4),
      ),
    );
  }

  /// 显示权限被永久拒绝的对话框
  Future<void> _showPermanentlyDeniedDialog(PermissionType type) async {
    final permissionName = type == PermissionType.camera ? '相机' : '相册';
    final iconData = type == PermissionType.camera ? Icons.camera_alt : Icons.photo_library;
    
    final result = await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(iconData, color: Colors.red.shade600),
            const SizedBox(width: 8),
            const Text('权限被拒绝'),
          ],
        ),
        content: Text('${permissionName}权限被永久拒绝，请在设置中手动开启。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue.shade600,
              foregroundColor: Colors.white,
            ),
            child: const Text('去设置'),
          ),
        ],
      ),
    );

    if (result == true) {
      await _permissionService.openSettings();
    }
  }

  /// 添加图片
  void _addImages(List<String> imagePaths) {
    final remainingSlots = 3 - _selectedImages.length;
    
    if (remainingSlots <= 0) {
      _showErrorSnackBar('最多只能选择3张图片，请先删除部分图片');
      return;
    }
    
    final imagesToAdd = imagePaths.take(remainingSlots).toList();
    
    setState(() {
      _selectedImages.addAll(imagesToAdd);
    });
    
    _logger.i('添加了${imagesToAdd.length}张图片，当前总数: ${_selectedImages.length}');
    
    // 提供友好的反馈
    if (imagePaths.length > remainingSlots) {
      _showInfoSnackBar('已添加${imagesToAdd.length}张图片，最多只能选择3张');
    } else {
      _showSuccessSnackBar('成功添加${imagesToAdd.length}张图片');
    }
  }

  /// 移除图片
  void _removeImage(int index) {
    setState(() {
      _selectedImages.removeAt(index);
    });
    _logger.d('移除了第${index + 1}张图片');
  }

  /// 继续到风格选择
  void _continueToStyleSelection() {
    _logger.i('继续到风格选择页面');
    // TODO: 导航到风格选择页面
    // context.router.push(StyleSelectionRoute(imagePaths: _selectedImages));
  }

  /// 显示错误提示
  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.error_outline, color: Colors.white),
            const SizedBox(width: 8),
            Expanded(child: Text(message)),
          ],
        ),
        backgroundColor: Colors.red.shade600,
        behavior: SnackBarBehavior.floating,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  /// 显示成功提示
  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.check_circle_outline, color: Colors.white),
            const SizedBox(width: 8),
            Expanded(child: Text(message)),
          ],
        ),
        backgroundColor: Colors.green.shade600,
        behavior: SnackBarBehavior.floating,
        duration: const Duration(seconds: 2),
      ),
    );
  }

  /// 显示信息提示
  void _showInfoSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.info_outline, color: Colors.white),
            const SizedBox(width: 8),
            Expanded(child: Text(message)),
          ],
        ),
        backgroundColor: Colors.blue.shade600,
        behavior: SnackBarBehavior.floating,
        duration: const Duration(seconds: 3),
      ),
    );
  }
}
