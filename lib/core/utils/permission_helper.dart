import 'dart:io';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:permission_handler/permission_handler.dart';

/// 权限助手类
class PermissionHelper {
  
  /// 检查并请求保存图片权限
  static Future<bool> requestSaveImagePermission(BuildContext context) async {
    try {
      if (Platform.isAndroid) {
        return await _handleAndroidSavePermission(context);
      } else if (Platform.isIOS) {
        return await _handleIOSSavePermission(context);
      }
      return true;
    } catch (e) {
      print('权限请求失败: $e');
      if (context.mounted) {
        _showErrorDialog(context, '权限请求失败', '无法获取权限：$e');
      }
      return false;
    }
  }

  /// 处理Android保存权限
  static Future<bool> _handleAndroidSavePermission(BuildContext context) async {
    final deviceInfo = await DeviceInfoPlugin().androidInfo;
    final sdkInt = deviceInfo.version.sdkInt;

    Permission permission;
    String permissionName;
    String explanation;

    if (sdkInt >= 33) { // Android 13+
      permission = Permission.photos;
      permissionName = '相册';
      explanation = '为了将生成的婚纱照保存到您的设备，Hera需要访问您的相册。';
    } else { // Android 12 or older
      permission = Permission.storage;
      permissionName = '存储';
      explanation = '为了将图片保存到您的设备，Hera需要存储权限。';
    }

    return await _requestPermission(context, permission, permissionName, explanation);
  }

  /// 处理iOS保存权限
  static Future<bool> _handleIOSSavePermission(BuildContext context) async {
    return await _requestPermission(
      context,
      Permission.photos,
      '相册',
      '为了将生成的美丽婚纱照保存到您的"照片"应用中，Hera需要访问您的相册。',
    );
  }
  
  /// 检查并请求相机权限
  static Future<bool> requestCameraPermission(BuildContext context) async {
    try {
      return await _requestPermission(
        context,
        Permission.camera,
        '相机',
        '为了拍摄用于生成婚纱照的原始照片，Hera需要使用您的相机。',
      );
    } catch (e) {
      print('相机权限请求失败: $e');
      if (context.mounted) {
        _showErrorDialog(context, '权限请求失败', '无法获取相机权限：$e');
      }
      return false;
    }
  }
  
  /// 通用权限请求逻辑
  static Future<bool> _requestPermission(
    BuildContext context,
    Permission permission,
    String permissionName,
    String explanation,
  ) async {
    PermissionStatus status = await permission.status;

    if (status.isGranted) {
      return true;
    }

    if (status.isDenied) {
      if (context.mounted) {
        final shouldRequest = await _showPermissionExplanationDialog(
          context,
          '需要$permissionName权限',
          explanation,
        );
        if (!shouldRequest) return false;
      }
      status = await permission.request();
    }

    if (status.isGranted) {
      return true;
    }
    
    if (status.isPermanentlyDenied && context.mounted) {
      _showPermissionSettingsDialog(context, permissionName);
      return false;
    }

    return false;
  }

  /// 显示权限说明对话框
  static Future<bool> _showPermissionExplanationDialog(
    BuildContext context,
    String title,
    String message,
  ) async {
    return await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16.r)),
        title: Row(
          children: [
            const Icon(Icons.shield_outlined, color: Colors.blue),
            SizedBox(width: 8.w),
            Text(title, style: TextStyle(fontSize: 18.sp, fontWeight: FontWeight.bold)),
          ],
        ),
        content: Text(message, style: TextStyle(fontSize: 14.sp)),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('残忍拒绝'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8.r)),
            ),
            child: const Text('好的，去授权'),
          ),
        ],
      ),
    ) ?? false;
  }

  /// 显示需要到设置页面开启权限的对话框
  static void _showPermissionSettingsDialog(BuildContext context, String permissionName) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16.r)),
        title: Row(
          children: [
            const Icon(Icons.settings_suggest, color: Colors.orange),
            SizedBox(width: 8.w),
            Text('需要手动开启权限', style: TextStyle(fontSize: 18.sp, fontWeight: FontWeight.bold)),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('您已永久拒绝了$permissionName权限，需要手动在系统设置中开启才能继续使用该功能。', style: TextStyle(fontSize: 14.sp)),
            SizedBox(height: 12.h),
            Container(
              padding: EdgeInsets.all(12.w),
              decoration: BoxDecoration(
                color: Colors.grey.shade100,
                borderRadius: BorderRadius.circular(8.r),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text('操作步骤：', style: TextStyle(fontWeight: FontWeight.bold, color: Colors.grey.shade700, fontSize: 13.sp)),
                  SizedBox(height: 8.h),
                  Text('1. 点击 "去设置" 按钮', style: TextStyle(fontSize: 12.sp)),
                  Text('2. 在应用设置中找到 "权限"', style: TextStyle(fontSize: 12.sp)),
                  Text('3. 开启 "$permissionName" 权限', style: TextStyle(fontSize: 12.sp)),
                  Text('4. 返回 Hera 应用即可', style: TextStyle(fontSize: 12.sp)),
                ],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('我知道了'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              openAppSettings();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.orange,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8.r)),
            ),
            child: const Text('去设置'),
          ),
        ],
      ),
    );
  }

  /// 显示错误对话框
  static void _showErrorDialog(BuildContext context, String title, String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16.r)),
        title: Row(
          children: [
            const Icon(Icons.error_outline, color: Colors.red),
            SizedBox(width: 8.w),
            Text(title, style: TextStyle(fontSize: 18.sp, fontWeight: FontWeight.bold)),
          ],
        ),
        content: Text(message, style: TextStyle(fontSize: 14.sp)),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }
} 