import 'dart:io';
import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../core/utils/image_picker_service.dart';

/// 图片上传组件
class ImageUploadWidget extends StatefulWidget {
  final List<File> initialImages;
  final Function(List<File>) onImagesChanged;
  final int maxImages;
  final bool allowMultiple;
  final String? placeholder;

  const ImageUploadWidget({
    super.key,
    this.initialImages = const [],
    required this.onImagesChanged,
    this.maxImages = 5,
    this.allowMultiple = true,
    this.placeholder,
  });

  @override
  State<ImageUploadWidget> createState() => _ImageUploadWidgetState();
}

class _ImageUploadWidgetState extends State<ImageUploadWidget> {
  List<File> _images = [];

  @override
  void initState() {
    super.initState();
    _images = List.from(widget.initialImages);
  }

  @override
  void didUpdateWidget(ImageUploadWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.initialImages != widget.initialImages) {
      _images = List.from(widget.initialImages);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (_images.isEmpty)
          _buildEmptyState()
        else
          _buildImageGrid(),
        if (_images.isNotEmpty && _images.length < widget.maxImages) ...[
          SizedBox(height: 16.h),
          _buildAddButton(),
        ],
      ],
    );
  }

  /// 构建空状态
  Widget _buildEmptyState() {
    return GestureDetector(
      onTap: _showImagePicker,
      child: Container(
        width: double.infinity,
        height: 200.h,
        decoration: BoxDecoration(
          border: Border.all(
            color: Colors.grey.shade300,
            width: 2,
          ),
          borderRadius: BorderRadius.circular(12.r),
        ),
        child: CustomPaint(
          painter: DashedBorderPainter(
            color: Colors.grey.shade400,
            strokeWidth: 2,
            dashLength: 8,
            spacing: 4,
          ),
          child: Container(
            decoration: BoxDecoration(
              color: Colors.grey.shade50,
              borderRadius: BorderRadius.circular(12.r),
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.cloud_upload_outlined,
                  size: 48.sp,
                  color: Colors.grey.shade400,
                ),
                SizedBox(height: 16.h),
                Text(
                  widget.placeholder ?? '点击上传图片',
                  style: TextStyle(
                    fontSize: 16.sp,
                    color: Colors.grey.shade600,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                SizedBox(height: 8.h),
                Text(
                  '支持JPG、PNG格式，最多${widget.maxImages}张',
                  style: TextStyle(
                    fontSize: 12.sp,
                    color: Colors.grey.shade500,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 构建图片网格
  Widget _buildImageGrid() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '已选择图片 (${_images.length}/${widget.maxImages})',
          style: TextStyle(
            fontSize: 14.sp,
            color: Colors.grey.shade700,
            fontWeight: FontWeight.w500,
          ),
        ),
        SizedBox(height: 12.h),
        GridView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 3,
            crossAxisSpacing: 8.w,
            mainAxisSpacing: 8.h,
            childAspectRatio: 1,
          ),
          itemCount: _images.length,
          itemBuilder: (context, index) => _buildImageItem(_images[index], index),
        ),
      ],
    );
  }

  /// 构建图片项
  Widget _buildImageItem(File image, int index) {
    return Stack(
      children: [
        Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8.r),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(8.r),
            child: Image.file(
              image,
              width: double.infinity,
              height: double.infinity,
              fit: BoxFit.cover,
            ),
          ),
        ),
        Positioned(
          top: 4,
          right: 4,
          child: GestureDetector(
            onTap: () => _removeImage(index),
            child: Container(
              width: 24.w,
              height: 24.w,
              decoration: BoxDecoration(
                color: Colors.red.withOpacity(0.8),
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.close,
                size: 16.sp,
                color: Colors.white,
              ),
            ),
          ),
        ),
      ],
    );
  }

  /// 构建添加按钮
  Widget _buildAddButton() {
    return GestureDetector(
      onTap: _showImagePicker,
      child: Container(
        width: double.infinity,
        height: 50.h,
        decoration: BoxDecoration(
          border: Border.all(
            color: Theme.of(context).primaryColor.withOpacity(0.3),
            width: 1,
          ),
          borderRadius: BorderRadius.circular(8.r),
          color: Theme.of(context).primaryColor.withOpacity(0.05),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.add_photo_alternate_outlined,
              size: 20.sp,
              color: Theme.of(context).primaryColor,
            ),
            SizedBox(width: 8.w),
            Text(
              '继续添加图片',
              style: TextStyle(
                fontSize: 14.sp,
                color: Theme.of(context).primaryColor,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 显示图片选择器
  void _showImagePicker() async {
    if (widget.allowMultiple && _images.length < widget.maxImages) {
      final remainingSlots = widget.maxImages - _images.length;
      
      if (remainingSlots > 1) {
        // 显示选择菜单：单张、多张
        _showImagePickerOptions();
      } else {
        // 只能选择一张
        final image = await ImagePickerService.showImagePickerBottomSheet(context);
        if (image != null) {
          _addImage(image);
        }
      }
    } else {
      // 单图模式或已达上限
      final image = await ImagePickerService.showImagePickerBottomSheet(context);
      if (image != null) {
        if (!widget.allowMultiple) {
          _images.clear();
        }
        _addImage(image);
      }
    }
  }

  /// 显示图片选择选项
  void _showImagePickerOptions() {
    showModalBottomSheet(
      context: context,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20.r)),
      ),
      builder: (context) => Container(
        padding: EdgeInsets.all(20.w),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 40.w,
              height: 4.h,
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2.r),
              ),
            ),
            SizedBox(height: 20.h),
            Text(
              '选择图片',
              style: TextStyle(
                fontSize: 18.sp,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 20.h),
            _buildOptionTile(
              icon: Icons.camera_alt,
              title: '拍照',
              subtitle: '使用相机拍摄',
              onTap: () async {
                Navigator.pop(context);
                final image = await ImagePickerService.pickImageFromCamera(context);
                if (image != null) {
                  _addImage(image);
                }
              },
            ),
            _buildOptionTile(
              icon: Icons.photo,
              title: '选择单张',
              subtitle: '从相册选择一张图片',
              onTap: () async {
                Navigator.pop(context);
                final image = await ImagePickerService.pickImageFromGallery(context);
                if (image != null) {
                  _addImage(image);
                }
              },
            ),
            _buildOptionTile(
              icon: Icons.photo_library,
              title: '选择多张',
              subtitle: '从相册选择多张图片',
              onTap: () async {
                Navigator.pop(context);
                final remainingSlots = widget.maxImages - _images.length;
                final images = await ImagePickerService.pickMultipleImages(
                  context,
                  limit: remainingSlots,
                );
                for (final image in images) {
                  _addImage(image);
                }
              },
            ),
            SizedBox(height: 20.h),
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('取消'),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建选项项
  Widget _buildOptionTile({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    return ListTile(
      leading: Container(
        width: 48.w,
        height: 48.w,
        decoration: BoxDecoration(
          color: Theme.of(context).primaryColor.withOpacity(0.1),
          borderRadius: BorderRadius.circular(12.r),
        ),
        child: Icon(
          icon,
          color: Theme.of(context).primaryColor,
          size: 24.sp,
        ),
      ),
      title: Text(
        title,
        style: TextStyle(
          fontSize: 16.sp,
          fontWeight: FontWeight.w600,
        ),
      ),
      subtitle: Text(
        subtitle,
        style: TextStyle(
          fontSize: 12.sp,
          color: Colors.grey.shade600,
        ),
      ),
      onTap: onTap,
    );
  }

  /// 添加图片
  void _addImage(File image) {
    if (_images.length < widget.maxImages) {
      setState(() {
        _images.add(image);
      });
      widget.onImagesChanged(_images);
    }
  }

  /// 移除图片
  void _removeImage(int index) {
    setState(() {
      _images.removeAt(index);
    });
    widget.onImagesChanged(_images);
  }
}

/// 自定义虚线边框绘制器
class DashedBorderPainter extends CustomPainter {
  final Color color;
  final double strokeWidth;
  final double dashLength;
  final double spacing;

  DashedBorderPainter({
    required this.color,
    required this.strokeWidth,
    required this.dashLength,
    required this.spacing,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..strokeWidth = strokeWidth
      ..style = PaintingStyle.stroke;

    _drawDashedPath(
      canvas,
      paint,
      Path()
        ..addRRect(
          RRect.fromRectAndRadius(
            Rect.fromLTWH(0, 0, size.width, size.height),
            Radius.circular(12),
          ),
        ),
    );
  }

  void _drawDashedPath(Canvas canvas, Paint paint, Path path) {
    final pathMetrics = path.computeMetrics();
    for (final pathMetric in pathMetrics) {
      double distance = 0.0;
      while (distance < pathMetric.length) {
        final nextDistance = math.min(distance + dashLength, pathMetric.length);
        final extractedPath = pathMetric.extractPath(distance, nextDistance);
        canvas.drawPath(extractedPath, paint);
        distance = nextDistance + spacing;
      }
    }
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
} 